# .github/workflows/build_prod.yml
name: 【本番環境】用にビルドする

on:
  push:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest
    name: ビルドとPUSH
    steps:
      - name: git-checkout
        uses: actions/checkout@v2

      - name: パッケージのインストール
        run: npm install

      - name: 環境変数の指定
        run: cp .env.production.example .env.production

      - name: ビルド
        run: npm run build # The build command of your project

      - name: Push
        uses: s0/git-publish-subdir-action@develop
        env:
          REPO: self
          BRANCH: build-prod # The branch name where you want to push the assets
          FOLDER: build # The directory where your assets are generated
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} # GitHub will automatically add this - you don't need to bother getting a token
          MESSAGE: "Build: ({sha}) {msg}" # The commit message
