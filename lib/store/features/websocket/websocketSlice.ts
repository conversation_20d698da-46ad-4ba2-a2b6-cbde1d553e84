import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { AppThunk } from "@/lib/store";
import { sessionStorageKey } from "@/constants/session";
import { IItem, IPositionSummary } from "@/lib/common";
import { SessionStatus, SocketStatus } from "@/constants/enum";

export interface IWebSocketState {
  sessionStatus: SessionStatus;
  status: SocketStatus;
  sessionID: string;
  // 会話履歴
  items: IItem[];
  // ユーザーへの提案ポジションリスト（ポジション検索結果＋おすすめ）
  positions: IPositionSummary[];
  currentPage: string;
}

const initialState: IWebSocketState = {
  sessionStatus: SessionStatus.IN_CONVERSATION,
  status: SocketStatus.Unknown,
  sessionID: "",
  // 会話履歴アイテム（メッセージアイテムか、ポジション検索結果アイテム）
  items: [],
  positions: [],
  currentPage: "",
};

const websocketSlice = createSlice({
  name: "websocket",
  initialState,
  reducers: {
    setSessionStatus(state, action: PayloadAction<SessionStatus>) {
      state.sessionStatus = action.payload;
    },
    setStatus(state, action: PayloadAction<SocketStatus>) {
      state.status = action.payload;
    },
    setConnected: (state, action: PayloadAction<string | null>) => {
      state.status = SocketStatus.Connected;
      state.sessionID = action.payload ?? "";
    },
    setDisconnected: (state) => {
      state.status = SocketStatus.Disconnected;
      state.sessionID = "";
      state.positions = [];
      state.currentPage = "";
    },
    setSessionID: (state, action: PayloadAction<string>) => {
      state.sessionID = action.payload;
    },
    updateItem: (
      state,
      action: PayloadAction<{ newItem: IItem; positionID?: string | null }>
    ) => {
      // チャットのレスポンスは来る前に、メインチャット<=>ポジション詳細チャットが切り替わった可能性がありますので、
      // 受け取ったレスポンスは必ず今表示中画面のチャットのレスポンスに限らないので、常にレスポンスの内容(positionID)を見て、どのアイテムを更新するかを判断する。
      if (action.payload.positionID) {
        // ポジション詳細チャットの会話履歴更新
        // ユーザーへの提案ポジションリストからIDがaction.payload.positionIDのアイテムを更新します
        const positionIndex = state.positions.findIndex(
          (pos) => pos.ID.toString() === action.payload.positionID
        );

        if (positionIndex !== -1) {
          const position = state.positions[positionIndex];
          const existingIndex = position.items.findIndex(
            (item) => item.itemId === action.payload.newItem.itemId
          );

          if (existingIndex !== -1) {
            // 既存アイテム更新
            state.positions[positionIndex].items[existingIndex] = {
              ...state.positions[positionIndex].items[existingIndex],
              message:
                state.positions[positionIndex].items[existingIndex].message +
                action.payload.newItem.message,
            };
          } else {
            // 新規アイテム追加
            state.positions[positionIndex].items.push({
              role: action.payload.newItem.role,
              itemId: action.payload.newItem.itemId,
              message: action.payload.newItem.message,
            });
          }
        } else {
          // ポジションが見つからなったが、基本不可能
          console.error("ポジションが見つからなった");
        }
      } else {
        // メインチャットの会話履歴更新
        if (action.payload.newItem.positionSearchResult) {
          // ポジション検索結果の場合
          const { Positions } = action.payload.newItem.positionSearchResult;

          Positions.forEach((newPosition) => {
            // ユーザーへの提案ポジションリスト更新
            const existingIndex = state.positions.findIndex(
              (pos) => pos.ID === newPosition.ID
            );

            if (existingIndex !== -1) {
              // 既存ポジションを更新しますが、itemsはそのまま
              state.positions[existingIndex] = {
                ...state.positions[existingIndex],
                ...newPosition,
                items: state.positions[existingIndex].items,
              };
            } else {
              // itemsが空の新規ポジション追加
              state.positions.push({
                ...newPosition,
                items: [],
              });
            }
          });

          //
          const positionReferences = Positions.map((pos) => {
            const statePosition = state.positions.find((p) => p.ID === pos.ID);
            return statePosition || pos;
          });

          // 複数回のポジション検索結果に同じポジションが入っている可能性がありますので、
          // ユーザーへの提案ポジションリストより全体的に管理しているので、その中のポジションのreference利用
          action.payload.newItem.positionSearchResult = {
            ...action.payload.newItem.positionSearchResult,
            Positions: positionReferences,
          };
        }

        const existingIndex = state.items.findIndex(
          (item) => item.itemId === action.payload.newItem.itemId
        );

        if (existingIndex !== -1) {
          // 既存アイテム（メッセージアイテムか、ポジション検索結果アイテム）の更新
          state.items[existingIndex] = {
            ...state.items[existingIndex],
            message:
              state.items[existingIndex].message +
              action.payload.newItem.message,
            // positionSearchResultがある場合はマージして更新
            ...(action.payload.newItem.positionSearchResult && {
              positionSearchResult: {
                ...action.payload.newItem.positionSearchResult,
              },
            }),
          };
        } else {
          // 新規アイテム（メッセージアイテムか、ポジション検索結果アイテム）加
          state.items.push({
            role: action.payload.newItem.role,
            itemId: action.payload.newItem.itemId,
            message: action.payload.newItem.message,
            positionSearchResult: action.payload.newItem.positionSearchResult,
          });
        }
      }
    },
    setCurrentPage: (state, action: PayloadAction<string>) => {
      state.currentPage = action.payload;
    },
    updatePositions: (state, action: PayloadAction<IPositionSummary[]>) => {
      // ユーザーへの提案ポジションリスト更新
      action.payload.forEach((newPosition) => {
        const existingIndex = state.positions.findIndex(
          (pos) => pos.ID === newPosition.ID
        );

        if (existingIndex !== -1) {
          // 既存ポジションを更新しますが、itemsはそのまま
          state.positions[existingIndex] = {
            ...state.positions[existingIndex],
            ...newPosition,
            items: state.positions[existingIndex].items,
          };
        } else {
          // itemsが空の新規ポジション追加
          state.positions.push({
            ...newPosition,
            items: [],
          });
        }
      });
    },
  },
});

export const saveSessionID =
  (sessionID: string): AppThunk =>
  (dispatch) => {
    localStorage.setItem(sessionStorageKey, sessionID);
    dispatch(websocketSlice.actions.setSessionID(sessionID));
  };

export const {
  setSessionStatus,
  setStatus,
  setConnected,
  setDisconnected,
  setSessionID,
  updatePositions,
  updateItem,
  setCurrentPage,
} = websocketSlice.actions;
export default websocketSlice.reducer;
