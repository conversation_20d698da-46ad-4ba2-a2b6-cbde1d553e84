import { createSlice } from "@reduxjs/toolkit";

export interface GlobalState {
  versionToastClosed: boolean;
}

const initialState: GlobalState = {
  versionToastClosed: false,
};

const globalStateSlice = createSlice({
  name: "globalState",
  initialState,
  reducers: {
    closeVersionToast: (state) => {
      state.versionToastClosed = true;
    },
  },
});

export const { closeVersionToast } = globalStateSlice.actions;
export default globalStateSlice.reducer;
