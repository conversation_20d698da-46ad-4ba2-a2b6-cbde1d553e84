import { CareerAgentAPI } from "./api";

let socket: CareerAgentAPI | null = null;

export const setSocket = (ws: CareerAgentAPI) => {
  socket = ws;
};

export const sendWebSocketMessage = (
  message: string,
  previousPage: string,
  currentPage: string,
  positionID?: string | null
): boolean => {
  if (socket && socket.isConnected()) {
    console.debug("sendWebSocketMessage with message =", message);

    const input = {
      prev_page: previousPage,
      current_page: currentPage,
      position_id: positionID,
      message: message,
    };

    socket?.send(JSON.stringify(input));

    return true;
  } else {
    console.warn("[WebSocket] Tried to send but socket is not open");

    return false;
  }
};
