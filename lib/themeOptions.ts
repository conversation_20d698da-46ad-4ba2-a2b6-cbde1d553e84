const themeOptions: object = {
  cssVariables: true,
  palette: {
    mode: "light",
    primary: {
      main: "#7FAFE2",
    },
    secondary: {
      main: "#e26486",
    },
    text: {
      primary: "#17202a",
      secondary: "#29394b",
      disabled: "#4f5b69",
      hint: "#91a7c0",
    },
    background: {
      default: "#dcedff",
    },
    error: {
      main: "#ff637d",
    },
  },
  // typography: {
  //   fontFamily: "Noto Sans JP",
  // },
  props: {
    MuiAppBar: {
      color: "transparent",
    },
  },
};

export default themeOptions;
