import "./VersionToast.scss";
import { useState } from "react";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Button from "@mui/material/Button";
import InfoIcon from "@mui/icons-material/Info";
import Box from "@mui/material/Box";

type CloseHandler = () => void;

interface VersionToastProps {
  onClose: CloseHandler;
  versionName: string;
  features: string[];
}

export default function VersionToast({
  versionName,
  features,
  onClose,
}: VersionToastProps) {
  const [open, setOpen] = useState(true);

  if (!open) {
    return null;
  }

  const handleClick = () => {
    setOpen(false);
    onClose();
  };

  return (
    <Box className="version-toast-container">
      <Card className="card">
        <CardContent className="card-content">
          <Box className="info">
            <InfoIcon color="primary" />
          </Box>
          <Box className="text-container">
            <h3>バージョン: {versionName}</h3>
            <p>このバージョンでできること：</p>
            <ul>
              {features.map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
            <Box className="footer">
              <Button variant="contained" onClick={handleClick}>
                了解しました
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
}
