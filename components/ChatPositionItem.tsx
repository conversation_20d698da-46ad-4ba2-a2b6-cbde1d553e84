import { useCallback, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/lib/store/hooks";
import Box from "@mui/material/Box";
import PositionCardList from "@/components/PositionCardList";
import { ChatMessage } from "@/components/ChatMessage";
import RecommendationList from "@/components/positions/recommendations/RecommendationList";
import { ChatMessageRole } from "@/constants/enum";
import { IItem } from "@/lib/common";
import { updateItem } from "@/lib/store/features/websocket/websocketSlice";
import { fetchApiData } from "@/utils/fetch";

interface IChatPositionItemProps {
  item: IItem;
}

export default function ChatPositionItem({ item }: IChatPositionItemProps) {
  const dispatch = useAppDispatch();
  const [loadingMore, setLoadingMore] = useState(false);
  const positions = useAppSelector(
    (state) =>
      state.websocket.items.find((i) => i.itemId === item.itemId)
        ?.positionSearchResult?.Positions ?? []
  );

  const handleLoadMore = useCallback(async () => {
    if (positions.length === 0) {
      return;
    }

    setLoadingMore(true);
    try {
      const res = await fetchApiData(
        `positions/search/${item.positionSearchResult!.SearchKey}/${positions.length}`,
        "求人検索が失敗しました"
      );
      if (res.data?.Positions) {
        dispatch(
          updateItem({
            newItem: {
              ...item,
              positionSearchResult: {
                ...item.positionSearchResult!,
                TotalPositionCount: res.data.TotalPositionCount,
                Positions: [
                  ...positions,
                  ...res.data.Positions.filter(
                    (p: any) => !positions.some((orig) => orig.ID === p.ID)
                  ),
                ],
              },
            },
            positionID: null,
          })
        );
      }
    } catch {}
    setLoadingMore(false);
  }, [dispatch, positions, item]);

  const more = item.positionSearchResult!.TotalPositionCount > positions.length;
  const Recommendations = item.positionSearchResult?.Recommendations || [];
  const searchKey = item.positionSearchResult?.SearchKey || "";
  return (
    <div key={item.itemId}>
      {positions.length > 0 && (
        <Box
          className="chat-message-container"
          key={item.itemId + "_positions"}
        >
          <PositionCardList positions={positions} />
          {more && (
            <Box sx={{ textAlign: "center", mt: 1 }}>
              <button
                onClick={handleLoadMore}
                disabled={loadingMore}
                style={{
                  padding: "6px 16px",
                  borderRadius: 4,
                  border: "1px solid #1976d2",
                  background: loadingMore ? "#eee" : "#1976d2",
                  color: loadingMore ? "#888" : "#fff",
                  cursor: loadingMore ? "not-allowed" : "pointer",
                }}
              >
                {loadingMore ? "読み込み中..." : "もっと見る"}
              </button>
            </Box>
          )}
        </Box>
      )}
      {positions.length === 0 && (
        <Box
          className="chat-message-container"
          key={item.itemId + "_no_positions"}
        >
          <ChatMessage
            showIcon={false}
            role={ChatMessageRole.Agent}
            message="申し訳ございません、ご希望の条件にピッタリの求人は見つかりませんでした。ただ、ご希望に近い条件の求人もございますので、それらの求人も含めてご提案させていただきます。"
          />
        </Box>
      )}
      {Recommendations.length > 0 && (
        <Box
          className="chat-message-container"
          key={item.itemId + "_recommendations"}
        >
          <RecommendationList
            searchKey={searchKey}
            recommendations={Recommendations}
          />
        </Box>
      )}
    </div>
  );
}
