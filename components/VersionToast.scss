.version-toast-container {
  position: fixed;
  top: 10vh;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 100;

  .card {
    width: 100%;
    max-width: 90vw;
    margin: 0 1rem;
    border-radius: 2rem;
    box-shadow: 0 0.2rem 0.6rem rgba(0, 0, 0, 0.1);
    background-color: #e3fffa;
    animation: version-toast-animation 0.5s ease-out forwards;
    min-width: var(--main-content-min-width);

    /* PCの場合の最大幅は500px */
    @media screen and (min-width: 1000px) {
      max-width: 500px;
    }

    .card-content {
      position: relative;
      display: flex;
      padding: 1rem !important;

      .info {
        margin-right: 1rem;
        display: flex;
        align-items: flex-start;
      }

      .text-container {
        flex: 1;

        h3 {
          font-size: 140%;
          font-weight: bold;
          margin-bottom: 1rem;
        }

        li {
          margin: 1rem 0;
          list-style-type: disc;
        }

        .footer {
          margin-top: 4rem;
          text-align: center;

          button {
            background-color: var(--variant-outlinedBorder);
          }
        }
      }
    }
  }
}

@keyframes version-toast-animation {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
