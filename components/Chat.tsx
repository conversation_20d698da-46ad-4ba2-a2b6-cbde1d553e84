"use client";

import "./Chat.scss";
import { useCallback, useEffect, useRef, useState } from "react";
import { ChatMessage } from "@/components/ChatMessage";
import UserInput from "@/components/UserInput";
import { useAppDispatch, useAppSelector } from "@/lib/store/hooks";
import Box from "@mui/material/Box";
import { PAGE_NAME } from "@/constants/page";
import { useRouter } from "next/navigation";
import { IItem } from "@/lib/common";
import { ChatMessageRole, SessionStatus, SocketStatus } from "@/constants/enum";
import { sendWebSocketMessage } from "@/lib/socket";
import {
  setCurrentPage,
  updateItem,
} from "@/lib/store/features/websocket/websocketSlice";
import ChatPositionItem from "@/components/ChatPositionItem";

// 一旦外す
// const MESSAGE_RESPONSE_TIMEOUT: number = 60000; // 60秒

export interface IChatProps {
  currentPage: string;
  positionID?: string | null;
}

export default function Chat(props: IChatProps) {
  const [dots, setDots] = useState<string>("");
  const bottomRef = useRef<HTMLDivElement>(null);
  // const messageTimeoutRef = useRef<
  //   string | number | NodeJS.Timeout | undefined
  // >(undefined);
  const intervalIdRef = useRef<string | number | NodeJS.Timeout | undefined>(
    undefined
  );
  const pageInitializedRef = useRef<string>("");

  const router = useRouter();
  const dispatch = useAppDispatch();
  const sessionStatus = useAppSelector(
    (state) => state.websocket.sessionStatus
  );
  const socketStatus = useAppSelector((state) => state.websocket.status);
  const isConnected = useAppSelector(
    (state) => state.websocket.status >= SocketStatus.Connected
  );
  const previousPage = useAppSelector((state) => state.websocket.currentPage);
  const allItems = useAppSelector((state) => state.websocket.items);
  const positions = useAppSelector((state) => state.websocket.positions);

  const send = useCallback(
    (message: string) => {
      sendWebSocketMessage(
        message,
        previousPage,
        props.currentPage,
        props.positionID
      );
    },
    [previousPage, props.currentPage, props.positionID]
  );

  const getItems = useCallback((): IItem[] => {
    if (props.currentPage == PAGE_NAME.CHAT) {
      // メインチャット
      return allItems;
    } else if (props.positionID) {
      // ポジション詳細チャット - look directly in state.positions
      const position = positions.find(
        (pos) => pos.ID.toString() === props.positionID
      );
      return position ? position.items : [];
    }

    return [];
  }, [allItems, positions, props.currentPage, props.positionID]);

  // メインチャットとポジション詳細チャット（ポジションIDあり）が以外の場合、強制的にメインチャットページに遷移する。
  useEffect(() => {
    if (props.currentPage == PAGE_NAME.CHAT) {
      // メインチャット
    } else if (
      props.currentPage == PAGE_NAME.POSITION_DETAIL &&
      props.positionID
    ) {
      // ポジション詳細チャット
    } else {
      // should not happen
      router.replace("/chat");
    }
  }, [router, allItems, props.currentPage, props.positionID]);

  // ページ初期化
  useEffect(() => {
    if (!isConnected) {
      return;
    }

    console.debug(`init when page ${props.currentPage} is showing`);

    // いまはグローバルステート`currentPage`はまだ遷移元ページ
    console.debug("previousPage", previousPage);
    console.debug("props.currentPage", props.currentPage);

    // ページ初期化処理の重複実行を防ぐためのフラグ
    const pageKey = `${props.currentPage}_${props.positionID || "main"}`;

    // すでに初期化された場合、スキップ
    if (pageInitializedRef.current === pageKey) {
      console.debug("ページ初期化済みのため、スキップ");
      return;
    }

    if (props.currentPage !== previousPage) {
      if (props.currentPage === PAGE_NAME.CHAT) {
        if (previousPage === "") {
          // 初めてのアクセスか画面リフレッシュ
          send("こんにちは");
        }
      } else if (props.currentPage === PAGE_NAME.POSITION_DETAIL) {
        if (!props.positionID) {
          // わざとポジションID抜きでの直URLアクセス。正常フローでは発生しないはず
          router.replace("/chat");
        }
      }

      // ページ初期化済み設定
      pageInitializedRef.current = pageKey;

      // グローバルステート`currentPage`設定
      dispatch(setCurrentPage(props.currentPage));
    }
  }, [
    router,
    dispatch,
    previousPage,
    props.currentPage,
    props.positionID,
    isConnected,
    send,
  ]);

  // Reset the page initialization ref when the page actually changes
  useEffect(() => {
    const pageKey = `${props.currentPage}_${props.positionID || "main"}`;
    if (pageInitializedRef.current && pageInitializedRef.current !== pageKey) {
      pageInitializedRef.current = "";
    }
  }, [props.currentPage, props.positionID]);

  // 画面表示項目（メッセージ）に変更があった場合、いつもページの最後に移動する。
  // TODO: この動きは良い？
  // 特にポジション検索結果が受信されたとき、一番目のポジション結果が見れるようにするのは正しそう
  // また、ポジション詳細画面から戻ってきたときに、メインチャットは受信がありますが、いま見たポジション詳細の位置で良いかもしれない。
  useEffect(() => {
    // const timeout = setTimeout(() => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth" });
    // }, 500);

    // return () => clearTimeout(timeout);
  }, [allItems, dots]);

  // websocketステータス変わったときの対応
  useEffect(() => {
    if (!isConnected || socketStatus == SocketStatus.MessageSent) {
      // show waiting sign
      if (!intervalIdRef.current) {
        intervalIdRef.current = setInterval(() => {
          setDots((prev) => {
            if (prev === "...") return ".";
            else return prev + ".";
          });
        }, 100);
      }

      // after user sent message
      // if (socketStatus == SocketStatus.MessageSent) {
      //   if (messageTimeoutRef.current) {
      //     clearTimeout(messageTimeoutRef.current);
      //   }
      //   messageTimeoutRef.current = setTimeout(() => {
      //     console.debug("messageTimeoutRef timed out");
      //   }, MESSAGE_RESPONSE_TIMEOUT);
      // }
    } else {
      if (intervalIdRef.current) {
        // clear waiting sign
        clearInterval(intervalIdRef.current);
        intervalIdRef.current = undefined;

        setDots("");
      }

      // if (messageTimeoutRef.current) {
      //   clearTimeout(messageTimeoutRef.current);
      // }
    }
  }, [socketStatus, isConnected]);

  const sendUserInput = (newValue: string) => {
    console.debug("sendUserInput newValue =", newValue);

    dispatch(
      updateItem({
        newItem: {
          role: ChatMessageRole.User,
          itemId: `input_${Date.now()}`,
          message: newValue,
        },
        positionID: props.positionID,
      })
    );

    send(newValue);
  };

  const displayItem = useCallback(
    (previousRole: string | null, item: IItem) => {
      if (sessionStatus == SessionStatus.IN_CONVERSATION) {
        if (item.positionSearchResult) {
          return <ChatPositionItem item={item} key={item.itemId} />;
        } else {
          return (
            <Box className="chat-message-container" key={item.itemId}>
              <ChatMessage
                showIcon={item.role === previousRole}
                role={item.role as ChatMessageRole}
                message={item.message}
              />
            </Box>
          );
        }
      } else {
        return (<Box></Box>);
      }
    },
    [sessionStatus]
  );

  return (
    <div className="chat-root">
      <div className="chat-messages-list">
        {(() => {
          const items = getItems();
          return items.map((chatMessage, i) => {
            const previousRole = i > 0 ? items[i - 1].role : null;
            return displayItem(previousRole, chatMessage);
          });
        })()}
        {!isConnected && (
          // || (startReceiving && !isReceiving) && (
          <div className="conversation-item typing-indicator">
            <div className="speaker-content">
              {!isConnected && <span>ネット接続中</span>}
              {dots}
              {!isConnected && (
                <p>
                  時間が経過しても接続できない場合、この画面を閉じて、再度開いてみてください
                </p>
              )}
            </div>
          </div>
        )}
        <div ref={bottomRef} />
      </div>
      {isConnected && <UserInput sendCallback={sendUserInput} />}
    </div>
  );
}
