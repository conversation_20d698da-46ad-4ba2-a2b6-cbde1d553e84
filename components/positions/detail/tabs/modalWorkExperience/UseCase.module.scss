@use "sass:list";
@use "@/scss/utils/variables";
@use "@/scss/utils/mixin";

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 311px;
  height: 251px;
  padding: 16px;
  overflow: hidden;
  background-color: variables.$white;
  text-align: left;
  gap: 16px;
}

.texts {
  position: relative;
  display: flex;
  flex-direction: column;
}

.index {
  font-size: 13px;
  font-weight: bold;
  line-height: 1; /* 13px */
}

.title {
  margin-top: 8px;
  font-size: 20px;
  font-weight: bold;
  line-height: 1; /* 20px */
}

.description {
  width: 225px;
  margin-top: 16px;
  font-size: 14px;
  line-height: 1.7; /* 23.8px */
}
