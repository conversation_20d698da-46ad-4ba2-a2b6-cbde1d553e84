import React from "react";
import { Box, Typography } from "@mui/material";
import RecommendationItem from "./RecommendationItem";
import { IPositionRecommendation } from "@/lib/common";

interface IRecommendationListProps {
  searchKey: string;
  recommendations: IPositionRecommendation[];
}

export default function RecommendationList({
  searchKey,
  recommendations,
}: IRecommendationListProps) {
  return (
    <Box
      sx={{
        border: 1,
        borderColor: "grey.300",
        borderRadius: 2,
        p: 3,
        backgroundColor: "background.paper",
      }}
    >
      <Typography
        variant="h6"
        gutterBottom
        align="center"
        sx={{ color: "success.main" }} // Use theme green
      >
        AIが提案するその他の検索条件
      </Typography>

      <Typography gutterBottom sx={{ pb: 3 }}>
        あなたの条件で検索している人は以下の条件でも探してます！
      </Typography>

      {recommendations.map((rec) => (
        <RecommendationItem
          key={rec.Theme}
          searchKey={searchKey}
          recommendation={rec}
        />
      ))}
    </Box>
  );
}
