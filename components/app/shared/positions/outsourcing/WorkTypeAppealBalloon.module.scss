@use "@/scss/utils/variables";
@use "@/scss/utils/mixin";

$colorBackground: #64b4c8;
$arrowHeight: 4px; // 吹き出しの高さ

.wrapper {
  padding-top: $arrowHeight;
}

.content {
  // 吹き出し
  &:before {
    position: absolute;
    top: -$arrowHeight;
    left: 8px;
    display: block;
    width: 0;
    height: 0;
    border-right: $arrowHeight solid transparent;
    border-bottom: $arrowHeight solid $colorBackground;
    border-left: $arrowHeight solid transparent;
    content: "";
  }

  position: relative;
  display: flex;
  align-items: center;
  width: fit-content;
  padding: 4px;
  background-color: $colorBackground;
  border-radius: 2px;
}

.icon {
  @include mixin.svgIcon(14px, variables.$white) {
    margin-right: 4px;
  }
}

.label {
  font-size: 11px;
  font-weight: bold;
  line-height: 1;
  color: variables.$white;
}
