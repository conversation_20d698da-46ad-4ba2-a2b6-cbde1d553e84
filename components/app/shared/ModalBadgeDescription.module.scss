@use "@/scss/utils/variables";
@use "@/scss/utils/mixin";

// --------------------------------------------------------------------------
// はたらく人ファースト宣言の説明モーダルコンポーネントのスタイル
// ---------------------------------------------------------------------------
.sh-<PERSON><PERSON>BadgeDescriptionContents {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  // PC
  @include mixin.pcLayout {
    padding: 24px;
  }
}

.sh-ModalHatarakuhitoFirstDescriptionTextarea {
  margin: 24px 0;
  text-align: start;

  & + & {
    margin-top: 16px;
  }
}

.sh-<PERSON>dalHatarakuhitoFirstDescriptionList {
  margin-top: 8px;

  & > li {
    position: relative;
    padding-left: 13px;
    line-height: 22px;

    & + li {
      margin-top: 8px;
    }

    &:before {
      position: absolute;
      top: 0;
      left: 0;
      display: inline;
      content: "・";
    }
  }
}
