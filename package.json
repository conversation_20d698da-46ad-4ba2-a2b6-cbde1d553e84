{"name": "aica_frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "export $(cat .env.local | xargs) && next build && node scripts/clean-mock-data.js", "build-dev": "export $(cat .env.dev | xargs) && next build && node scripts/clean-mock-data.js", "start": "next start", "lint": "npm run eslint ; npm run stylelint ; npm run typecheck ; npm run prettier", "eslint": "eslint . --cache", "eslint:fix": "eslint . --cache --fix", "stylelint": "stylelint --cache '**/*.{css,scss,sass}' || exit 0", "stylelint:fix": "stylelint --cache '**/*.{css,scss,sass}' --fix || exit 0", "typecheck": "tsc --noEmit", "prettier": "prettier --check .", "prettier:fix": "prettier --write ."}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.7", "@mui/joy": "5.0.0-beta.51", "@mui/material": "^6.4.7", "@mui/styled-engine-sc": "^6.4.6", "@reduxjs/toolkit": "^2.8.2", "@splidejs/react-splide": "^0.7.12", "axios": "^1.9.0", "md5": "^2.3.0", "next": "^15.2.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "smoothscroll-polyfill": "^0.4.4", "styled-components": "^6.1.15"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/md5": "^2.3.5", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/smoothscroll-polyfill": "^0.3.4", "eslint": "^9", "eslint-config-next": "15.2.0", "prettier": "^3.5.3", "sass": "^1.85.1", "stylelint": "^16.19.1", "tailwindcss": "^4", "typescript": "5.8.2"}, "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "sharp"]}}