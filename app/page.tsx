// import Link from "next/link";
// import CopyrightFooter from "@/components/CopyrightFooter";
import { redirect } from "next/navigation";

export default function Home() {
  // LPは不要なのでchatにリダイレクト
  const pathPrefix = process.env.PATH_PREFIX || "";
  redirect(`${pathPrefix}/chat`);
  // return (
  //   <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
  //     <main className="flex flex-col gap-8 row-start-2 items-center sm:items-start">
  //       <div className="flex gap-4 items-center flex-col sm:flex-row">
  //         こんにちは、私はミイダスのAIキャリアアドバイザーのAICAです。
  //       </div>

  //       <Link href="/chat" className="mx-auto">
  //         相談する
  //       </Link>
  //     </main>

  //     <footer className="row-start-3 flex gap-6 flex-wrap items-center justify-center">
  //       <CopyrightFooter />
  //     </footer>
  //   </div>
  // );
}
