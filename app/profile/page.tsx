"use client";

import { useRouter } from "next/navigation";
import { useForm, Controller } from "react-hook-form";
import TextField from "@mui/material/TextField";
import Grid from "@mui/material/Grid";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import LockIcon from "@mui/icons-material/Lock";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import IconButton from "@mui/material/IconButton";

// フォームデータ型定義
type ProfileFormData = {
  lastName: string;
  firstName: string;
  lastNameKana: string;
  firstNameKana: string;
  email: string;
  phone: string;
  password: string;
  birthYear: string;
  birthMonth: string;
  address: string;
};

export default function ProfilePage() {
  const router = useRouter();

  // React Hook Form の設定
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<ProfileFormData>({
    defaultValues: {
      lastName: "",
      firstName: "",
      lastNameKana: "",
      firstNameKana: "",
      email: "",
      phone: "",
      password: "",
      birthYear: "",
      birthMonth: "",
      address: "",
    },
    mode: "onBlur", // フォーカスが外れたときに検証
  });

  // フォーム送信時の処理
  const onSubmit = (data: ProfileFormData) => {
    console.debug("保存されたデータ:", data);
    // データを保存した後、チャットページに戻る
    router.push("/");
  };

  const handleCancel = () => {
    router.push("/");
  };

  return (
    <Box
      sx={{
        backgroundColor: "#f5f5f5",
        minHeight: "100vh",
        padding: 2,
      }}
    >
      <Box
        sx={{
          maxWidth: 600,
          margin: "0 auto",
          backgroundColor: "white",
          borderRadius: 2,
          boxShadow: "0 1px 3px rgba(0,0,0,0.12)",
        }}
      >
        <Box
          sx={{
            display: "flex",
            backgroundColor: "#e3f2fd",
            borderBottom: "1px solid #e0e0e0",
            padding: 2,
            position: "relative",
          }}
        >
          <IconButton
            onClick={handleCancel}
            sx={{
              position: "absolute",
              left: 8,
            }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography
            variant="h6"
            sx={{
              width: "100%",
              textAlign: "center",
            }}
          >
            基本情報
          </Typography>
        </Box>

        <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ p: 3 }}>
          <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
            <LockIcon fontSize="small" color="success" sx={{ mr: 1 }} />
            <Typography variant="body2" color="success">
              は自分が応募した企業にのみ表示されます
            </Typography>
          </Box>

          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="body2" gutterBottom>
                姓
                <LockIcon
                  fontSize="small"
                  color="success"
                  sx={{ ml: 0.5, verticalAlign: "middle", fontSize: 16 }}
                />
              </Typography>
              <Controller
                name="lastName"
                control={control}
                rules={{
                  required: "空白になっています",
                  pattern: {
                    value: /^[ぁ-んァ-ヶ一-龯]+$/,
                    message: "漢字、ひらがな、カタカナのみ入力可能です",
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    placeholder="姓を入力"
                    variant="outlined"
                    size="small"
                    error={!!errors.lastName}
                    helperText={errors.lastName?.message}
                    FormHelperTextProps={{ sx: { color: "error.main" } }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" gutterBottom>
                名
                <LockIcon
                  fontSize="small"
                  color="success"
                  sx={{ ml: 0.5, verticalAlign: "middle", fontSize: 16 }}
                />
              </Typography>
              <Controller
                name="firstName"
                control={control}
                rules={{
                  required: "空白になっています",
                  pattern: {
                    value: /^[ぁ-んァ-ヶ一-龯]+$/,
                    message: "漢字、ひらがな、カタカナのみ入力可能です",
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    placeholder="名を入力"
                    variant="outlined"
                    size="small"
                    error={!!errors.firstName}
                    helperText={errors.firstName?.message}
                    FormHelperTextProps={{ sx: { color: "error.main" } }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" gutterBottom>
                姓（カナ）
                <LockIcon
                  fontSize="small"
                  color="success"
                  sx={{ ml: 0.5, verticalAlign: "middle", fontSize: 16 }}
                />
              </Typography>
              <Controller
                name="lastNameKana"
                control={control}
                rules={{
                  required: "空白になっています",
                  pattern: {
                    value: /^[ァ-ヶー]+$/,
                    message: "カタカナのみ入力可能です",
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    placeholder="姓を入力"
                    variant="outlined"
                    size="small"
                    error={!!errors.lastNameKana}
                    helperText={errors.lastNameKana?.message}
                    FormHelperTextProps={{ sx: { color: "error.main" } }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" gutterBottom>
                名（カナ）
                <LockIcon
                  fontSize="small"
                  color="success"
                  sx={{ ml: 0.5, verticalAlign: "middle", fontSize: 16 }}
                />
              </Typography>
              <Controller
                name="firstNameKana"
                control={control}
                rules={{
                  required: "空白になっています",
                  pattern: {
                    value: /^[ァ-ヶー]+$/,
                    message: "カタカナのみ入力可能です",
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    placeholder="名を入力"
                    variant="outlined"
                    size="small"
                    error={!!errors.firstNameKana}
                    helperText={errors.firstNameKana?.message}
                    FormHelperTextProps={{ sx: { color: "error.main" } }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body2" gutterBottom>
                メールアドレス
                <LockIcon
                  fontSize="small"
                  color="success"
                  sx={{ ml: 0.5, verticalAlign: "middle", fontSize: 16 }}
                />
              </Typography>
              <Controller
                name="email"
                control={control}
                rules={{
                  required: "空白になっています",
                  pattern: {
                    value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                    message: "有効なメールアドレスを入力してください",
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    placeholder="メールアドレスを入力"
                    variant="outlined"
                    size="small"
                    error={!!errors.email}
                    helperText={errors.email?.message}
                    FormHelperTextProps={{ sx: { color: "error.main" } }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body2" gutterBottom>
                電話番号
                <LockIcon
                  fontSize="small"
                  color="success"
                  sx={{ ml: 0.5, verticalAlign: "middle", fontSize: 16 }}
                />
              </Typography>
              <Controller
                name="phone"
                control={control}
                rules={{
                  required: "空白になっています",
                  pattern: {
                    value: /^[0-9]{10,11}$/,
                    message: "有効な電話番号を入力してください（ハイフンなし）",
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    placeholder="電話番号を入力"
                    variant="outlined"
                    size="small"
                    error={!!errors.phone}
                    helperText={errors.phone?.message}
                    FormHelperTextProps={{ sx: { color: "error.main" } }}
                  />
                )}
              />
              <Typography variant="caption" color="text.secondary">
                ※ハイフンなし
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body2" gutterBottom>
                パスワード
              </Typography>
              <Controller
                name="password"
                control={control}
                rules={{
                  required: "空白になっています",
                  pattern: {
                    value: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/,
                    message:
                      "半角英数字を組み合わせ、8文字以上で入力してください",
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="password"
                    placeholder="パスワードを入力"
                    variant="outlined"
                    size="small"
                    error={!!errors.password}
                    helperText={errors.password?.message}
                    FormHelperTextProps={{ sx: { color: "error.main" } }}
                    inputProps={{
                      inputMode: "text",
                      autoComplete: "new-password",
                    }}
                  />
                )}
              />
              <Typography variant="caption" color="text.secondary">
                ※アルファベットと数字の組み合わせ8文字以上
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" gutterBottom>
                現在の年齢
              </Typography>
              <Controller
                name="birthYear"
                control={control}
                rules={{
                  required: "空白になっています",
                  pattern: {
                    value: /^[0-9]+$/,
                    message: "半角数字のみ入力可能です",
                  },
                  validate: (value) => {
                    const num = parseInt(value);
                    return (
                      (num >= 15 && num <= 90) ||
                      "15〜90の範囲で入力してください"
                    );
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    placeholder="○○"
                    variant="outlined"
                    size="small"
                    error={!!errors.birthYear}
                    helperText={errors.birthYear?.message}
                    FormHelperTextProps={{ sx: { color: "error.main" } }}
                    InputProps={{
                      endAdornment: (
                        <Typography variant="body2" color="textDisabled">
                          歳
                        </Typography>
                      ),
                      inputMode: "numeric",
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" gutterBottom>
                生まれ月
              </Typography>
              <Controller
                name="birthMonth"
                control={control}
                rules={{
                  required: "空白になっています",
                  pattern: {
                    value: /^[0-9]+$/,
                    message: "半角数字のみ入力可能です",
                  },
                  validate: (value) => {
                    const num = parseInt(value);
                    return (
                      (num >= 1 && num <= 12) ||
                      "1〜12の数字のみ入力してください"
                    );
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    placeholder="○○"
                    variant="outlined"
                    size="small"
                    error={!!errors.birthMonth}
                    helperText={errors.birthMonth?.message}
                    FormHelperTextProps={{ sx: { color: "error.main" } }}
                    InputProps={{
                      endAdornment: (
                        <Typography variant="body2" color="textDisabled">
                          月
                        </Typography>
                      ),
                      inputMode: "numeric",
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body2" gutterBottom>
                お住まいの市区町村名
              </Typography>
              <Typography variant="caption" color="text.secondary">
                例: 東京都新宿区、大阪府大阪市、北海道札幌市
              </Typography>
              <Controller
                name="address"
                control={control}
                rules={{
                  required: "空白になっています",
                  pattern: {
                    value:
                      /^([ぁ-んァ-ヶ一-龯]+)(都|道|府|県)([ぁ-んァ-ヶ一-龯]+)(市|区|町|村)$/,
                    message: "都道府県と市区町村の形式で入力してください",
                  },
                  maxLength: {
                    value: 15,
                    message: "15文字以内で入力してください",
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    placeholder="◯◯県◯◯市"
                    variant="outlined"
                    size="small"
                    error={!!errors.address}
                    helperText={errors.address?.message}
                    FormHelperTextProps={{ sx: { color: "error.main" } }}
                  />
                )}
              />
            </Grid>
          </Grid>

          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              mt: 4,
              borderTop: "1px solid #e0e0e0",
              pt: 2,
            }}
          >
            <Button
              onClick={handleCancel}
              variant="outlined"
              sx={{ width: "45%", borderRadius: 100 }}
            >
              キャンセル
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="success"
              sx={{
                width: "45%",
                borderRadius: 100,
                backgroundColor: "#4C576C",
              }}
            >
              保存する
            </Button>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}
