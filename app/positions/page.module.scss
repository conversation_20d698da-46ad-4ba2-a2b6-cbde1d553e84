@use "sass:list";
@use "@/scss/utils/variables";
@use "@/scss/utils/mixin";

.sectionItem {
  margin-bottom: 20px;
  background-color: variables.$white;
  box-shadow: 0 2px 6px rgba(55.97, 83.86, 92.38, 0.1);

  &:last-of-type {
    margin-bottom: 0;
  }
}

.positionTopSection {
  background-color: variables.$white;
}

.content {
  background-color: variables.$colorBlueGray50;
}

.blob {
  width: 100px;
  height: 100px;
  background: radial-gradient(circle at 30% 30%, #6ec5ff, #b388eb, #70f3ff);
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  animation: blobPulse 2s ease-in-out infinite;
  filter: blur(6px);
  opacity: 0.9;
}

@keyframes blobPulse {
  0%,
  100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: scale(1);
  }
  33% {
    border-radius: 40% 60% 70% 30% / 50% 60% 40% 60%;
    transform: scale(1.05);
  }
  66% {
    border-radius: 50% 50% 60% 40% / 60% 50% 50% 40%;
    transform: scale(0.98);
  }
}
