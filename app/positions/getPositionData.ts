"use client";

import { fetchApiData, readMockData } from "@/utils/fetch";

export async function getPositionData(positionId: string) {
  if (process.env.NEXT_PUBLIC_MOCK_API === "true") {
    return readMockData(
      "positions",
      positionId,
      "求人情報の取得に失敗しました",
    );
  }
  return fetchApiData(
    `positions/detail/${positionId}`,
    "求人情報の取得に失敗しました",
  );
}

export async function getCompanyData(positionId: string) {
  if (process.env.NEXT_PUBLIC_MOCK_API === "true") {
    return readMockData(
      "companies",
      positionId,
      "企業情報の取得に失敗しました",
    );
  }
  return fetchApiData(
    `companies/detail/${positionId}`,
    "企業情報の取得に失敗しました",
  );
}

export async function getBusinessData(positionId: string) {
  if (process.env.NEXT_PUBLIC_MOCK_API === "true") {
    return readMockData(
      "businesses",
      positionId,
      "事業情報の取得に失敗しました",
    );
  }
  return fetchApiData(
    `businesses/detail/${positionId}`,
    "事業情報の取得に失敗しました",
  );
}
