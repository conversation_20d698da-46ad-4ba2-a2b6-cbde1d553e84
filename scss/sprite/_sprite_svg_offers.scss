$icons: (
  sprite: (
    width: 501px,
    height: 392px,
    svgPath: "/assets/next/sprite/offers/sprite-99d54355.svg",
  ),
  ico_application: (
    width: 22px,
    height: 22px,
    backgroundX: -278px,
    backgroundY: -307px,
  ),
  ico_application_white: (
    width: 22px,
    height: 22px,
    backgroundX: -300px,
    backgroundY: -307px,
  ),
  ico_applied_offer: (
    width: 24px,
    height: 17px,
    backgroundX: -300px,
    backgroundY: -124px,
  ),
  ico_block: (
    width: 20px,
    height: 20px,
    backgroundX: -224px,
    backgroundY: -332px,
  ),
  ico_business_description: (
    width: 24px,
    height: 24px,
    backgroundX: -259px,
    backgroundY: -280px,
  ),
  ico_cal_dark: (
    width: 22px,
    height: 22px,
    backgroundX: -322px,
    backgroundY: -307px,
  ),
  ico_cancel: (
    width: 16px,
    height: 16px,
    backgroundX: -324px,
    backgroundY: -124px,
  ),
  ico_caution: (
    width: 18px,
    height: 16px,
    backgroundX: -475px,
    backgroundY: -304px,
  ),
  ico_check: (
    width: 14px,
    height: 10px,
    backgroundX: -486px,
    backgroundY: -205px,
  ),
  ico_delete: (
    width: 14px,
    height: 14px,
    backgroundX: -486px,
    backgroundY: -215px,
  ),
  ico_delete_white: (
    width: 20px,
    height: 20px,
    backgroundX: -244px,
    backgroundY: -332px,
  ),
  ico_emphasize: (
    width: 19px,
    height: 19px,
    backgroundX: -404px,
    backgroundY: -332px,
  ),
  ico_fee: (
    width: 24px,
    height: 24px,
    backgroundX: -283px,
    backgroundY: -280px,
  ),
  ico_follow: (
    width: 14px,
    height: 14px,
    backgroundX: -486px,
    backgroundY: -229px,
  ),
  ico_follow_blue: (
    width: 14px,
    height: 14px,
    backgroundX: -486px,
    backgroundY: -243px,
  ),
  ico_follow_white: (
    width: 14px,
    height: 14px,
    backgroundX: -486px,
    backgroundY: -257px,
  ),
  ico_income: (
    width: 20px,
    height: 20px,
    backgroundX: -264px,
    backgroundY: -332px,
  ),
  ico_info: (
    width: 18px,
    height: 18px,
    backgroundX: -290px,
    backgroundY: -142px,
  ),
  ico_jobtype: (
    width: 20px,
    height: 20px,
    backgroundX: -284px,
    backgroundY: -332px,
  ),
  ico_location: (
    width: 20px,
    height: 20px,
    backgroundX: -304px,
    backgroundY: -332px,
  ),
  ico_match_target: (
    width: 19px,
    height: 19px,
    backgroundX: -423px,
    backgroundY: -332px,
  ),
  ico_matched: (
    width: 19px,
    height: 19px,
    backgroundX: -442px,
    backgroundY: -332px,
  ),
  ico_meeting: (
    width: 25px,
    height: 24px,
    backgroundX: -475px,
    backgroundY: -280px,
  ),
  ico_meeting_white: (
    width: 22px,
    height: 22px,
    backgroundX: -344px,
    backgroundY: -307px,
  ),
  ico_notfound: (
    width: 24px,
    height: 24px,
    backgroundX: -307px,
    backgroundY: -280px,
  ),
  ico_offer_popup_skip: (
    width: 23px,
    height: 23px,
    backgroundX: -232px,
    backgroundY: -307px,
  ),
  ico_offer_popup_star: (
    width: 23px,
    height: 23px,
    backgroundX: -255px,
    backgroundY: -307px,
  ),
  ico_offer_search_result_empty: (
    width: 27px,
    height: 27px,
    backgroundX: -232px,
    backgroundY: -280px,
  ),
  ico_offer_sort_income: (
    width: 18px,
    height: 18px,
    backgroundX: -308px,
    backgroundY: -142px,
  ),
  ico_offer_sort_recent: (
    width: 18px,
    height: 18px,
    backgroundX: -326px,
    backgroundY: -142px,
  ),
  ico_position: (
    width: 20px,
    height: 18px,
    backgroundX: -270px,
    backgroundY: -142px,
  ),
  ico_premium_delete: (
    width: 14px,
    height: 14px,
    backgroundX: -340px,
    backgroundY: -124px,
  ),
  ico_premium_income: (
    width: 20px,
    height: 20px,
    backgroundX: -324px,
    backgroundY: -332px,
  ),
  ico_premium_jobtype: (
    width: 20px,
    height: 20px,
    backgroundX: -344px,
    backgroundY: -332px,
  ),
  ico_premium_location: (
    width: 20px,
    height: 20px,
    backgroundX: -364px,
    backgroundY: -332px,
  ),
  ico_premium_position: (
    width: 20px,
    height: 19px,
    backgroundX: -384px,
    backgroundY: -332px,
  ),
  ico_priority: (
    width: 30px,
    height: 30px,
    backgroundX: -300px,
    backgroundY: -94px,
  ),
  ico_priority_active: (
    width: 30px,
    height: 30px,
    backgroundX: -330px,
    backgroundY: -94px,
  ),
  ico_question: (
    width: 20px,
    height: 20px,
    backgroundX: -180px,
    backgroundY: -354px,
  ),
  ico_rec_application: (
    width: 22px,
    height: 22px,
    backgroundX: -180px,
    backgroundY: -332px,
  ),
  ico_rec_application_white: (
    width: 22px,
    height: 22px,
    backgroundX: -202px,
    backgroundY: -332px,
  ),
  ico_search: (
    width: 18px,
    height: 18px,
    backgroundX: -344px,
    backgroundY: -142px,
  ),
  ico_search_white: (
    width: 15px,
    height: 15px,
    backgroundX: -486px,
    backgroundY: -40px,
  ),
  ico_special: (
    width: 19px,
    height: 19px,
    backgroundX: -461px,
    backgroundY: -332px,
  ),
  ico_starred: (
    width: 14px,
    height: 14px,
    backgroundX: -352px,
    backgroundY: -160px,
  ),
  ico_will_employee_qty: (
    width: 15px,
    height: 15px,
    backgroundX: -486px,
    backgroundY: -55px,
  ),
  ico_will_employment_type: (
    width: 15px,
    height: 15px,
    backgroundX: -486px,
    backgroundY: -70px,
  ),
  ico_will_income: (
    width: 15px,
    height: 15px,
    backgroundX: -486px,
    backgroundY: -85px,
  ),
  ico_will_industry: (
    width: 15px,
    height: 15px,
    backgroundX: -486px,
    backgroundY: -100px,
  ),
  ico_will_jobtype: (
    width: 15px,
    height: 15px,
    backgroundX: -486px,
    backgroundY: -115px,
  ),
  ico_will_remote_work: (
    width: 15px,
    height: 15px,
    backgroundX: -486px,
    backgroundY: -130px,
  ),
  ico_will_skill: (
    width: 15px,
    height: 15px,
    backgroundX: -486px,
    backgroundY: -145px,
  ),
  ico_will_welfare: (
    width: 15px,
    height: 15px,
    backgroundX: -486px,
    backgroundY: -160px,
  ),
  ico_will_work_location: (
    width: 15px,
    height: 15px,
    backgroundX: -486px,
    backgroundY: -175px,
  ),
  ico_will_working_environment: (
    width: 15px,
    height: 15px,
    backgroundX: -486px,
    backgroundY: -190px,
  ),
  ico_wish_btn: (
    width: 24px,
    height: 24px,
    backgroundX: -331px,
    backgroundY: -280px,
  ),
  img_btn_appstore: (
    width: 109px,
    height: 40px,
    backgroundX: -366px,
    backgroundY: -280px,
  ),
  img_btn_playstore: (
    width: 135px,
    height: 40px,
    backgroundX: -366px,
    backgroundY: 0px,
  ),
  img_button: (
    width: 300px,
    height: 48px,
    backgroundX: 0px,
    backgroundY: -94px,
  ),
  img_copy_01: (
    width: 270px,
    height: 18px,
    backgroundX: 0px,
    backgroundY: -142px,
  ),
  img_copy_02: (
    width: 366px,
    height: 94px,
    backgroundX: 0px,
    backgroundY: 0px,
  ),
  img_notification_pc: (
    width: 120px,
    height: 120px,
    backgroundX: -366px,
    backgroundY: -40px,
  ),
  img_notification_sp: (
    width: 60px,
    height: 60px,
    backgroundX: 0px,
    backgroundY: -332px,
  ),
  img_offer_empty: (
    width: 232px,
    height: 172px,
    backgroundX: 0px,
    backgroundY: -160px,
  ),
  img_offer_not_satisfied_pc: (
    width: 120px,
    height: 120px,
    backgroundX: -366px,
    backgroundY: -160px,
  ),
  img_offer_not_satisfied_sp: (
    width: 60px,
    height: 60px,
    backgroundX: -60px,
    backgroundY: -332px,
  ),
  img_profile_pc: (
    width: 120px,
    height: 120px,
    backgroundX: -232px,
    backgroundY: -160px,
  ),
  img_profile_sp: (
    width: 60px,
    height: 60px,
    backgroundX: -120px,
    backgroundY: -332px,
  ),
);

////
/// <AUTHOR> Street
/// @group Sprite
////

/**
 * The following variable and function originate from the sass-mq library.
 * If you have already included it, you can eliminate the below
 * https://github.com/sass-mq/sass-mq/blob/master/_mq.scss
 */

/// Base font size on the `<body>` element
/// @type Number (unit)
$mq-base-font-size: 16px !default;

/// Convert pixels to ems
///
/// @param {Number} $px - value to convert
/// @param {Number} $base-font-size ($mq-base-font-size) - `<body>` font size
///
/// @example scss
///  $font-size-in-ems: mq-px2em(13px);
///  p { font-size: mq-px2em(13px); }
///
/// @requires $mq-base-font-size
/// @returns {Number}
@function mq-px2em($px, $base-font-size: $mq-base-font-size) {
  @if unitless($px) {
    @warn "Assuming #{$px} to be in pixels, attempting to convert it into pixels.";
    @return mq-px2em($px + 0px); // That may fail.
  } @else if unit($px) == em {
    @return $px;
  }
  @return ($px / $base-font-size) * 1em;
}

/// Set the `$sprite` map
/// @group sprite
$sprite: map-get($icons, sprite) !default;

/// Retrive an attributes value for a specific icon in the sprite map
/// @param {string} $icon - The name of the icon
/// @param {string} $attr - The attribute you wish to retrieve (e.g. width)
@function sprite-attr($icon, $attr) {
  $newIcon: map-get($icons, $icon);
  @if $newIcon == null {
    @warn "Can't find an icon with the name #{$icon}";
  }
  @return map-get($newIcon, $attr);
}

/// Create a map with the specified icon of attributes
/// @param {string} $icon - The name of the icon
@function icon-attr($icon) {
  $attr: (
    width: sprite-attr($icon, width),
    height: sprite-attr($icon, height),
    x: sprite-attr($icon, backgroundX),
    y: sprite-attr($icon, backgroundY),
  );

  @return $attr;
}

/// Get the width of an icon in em
/// @param {string} $icon - The name of the icon
@function icon_width($icon) {
  @return mq-px2em(sprite-attr($icon, width));
}

/// Get the height of an icon in em
/// @param {string} $icon - The name of the icon
@function icon_height($icon) {
  @return mq-px2em(sprite-attr($icon, height));
}

/// Assign the correct SVG background image and dimensions to the element
%sprite {
  background-image: url(map-get($sprite, svgPath));
  background-size: mq-px2em(map-get($sprite, width))
    mq-px2em(map-get($sprite, height));
}

/// Add an SVG sprite icon using em positioning
/// @param {string} $icon - The name of the icon
/// @param {string} $type [all] - The properties wanted (can be `all`, `size` or `bg`).
/// - `all` ensures the icon has the background, position and size.
/// - `size` is just for dimensions
/// - `bg` just  applies the backgrounds to an element
/// @example scss - Usage
/// .class {
/// 	@include sprite(arrow);
/// 	&:hover {
/// 		@include sprite(arrowDown, bg)
/// 	}
/// }
///
/// @example css - Output
/// .class {
/// 	display: inline-block;
/// 	background-image: url("../img/sprite.svg");
/// 	background-size: 34.25em 32.1875em;
/// }
///
///
/// .class {
/// 	width: 1.3125em;
/// 	height: 1.3125em;
/// 	background-position: -0.3125em -0.3125em;
/// }
/// .class:hover {
/// 	background-position: -2.25em -2.25em;
/// }
@mixin sprite($icon, $type: all, $useEm: no) {
  @if $type == all {
    @if $useEm == no {
      background-image: url(map-get($sprite, svgPath));
      background-size: map-get($sprite, width) map-get($sprite, height);
    } @else {
      // Shares the backgrounds
      @extend %sprite;
    }
  }

  $iconMap: icon-attr($icon);

  // Outputs dimensions in em
  @if $type == all or $type == size {
    @if $useEm == no {
      width: map-get($iconMap, width);
      height: map-get($iconMap, height);
    } @else {
      width: mq-px2em(map-get($iconMap, width) + 1);
      height: mq-px2em(map-get($iconMap, height) + 1);
    }
  }

  // Outputs background position in em
  @if $type == all or $type == bg {
    @if $useEm == no {
      background-position: map-get($iconMap, x) map-get($iconMap, y);
    } @else {
      background-position: mq-px2em(map-get($iconMap, x) - 5)
        mq-px2em(map-get($iconMap, y) - 5);
    }
  }
}
