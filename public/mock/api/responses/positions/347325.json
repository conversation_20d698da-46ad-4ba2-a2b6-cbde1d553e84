{"Position": {"ID": 347325, "CompanyID": 338675, "CompanySectionID": 266949, "BusinessID": 489811, "IsDetailShowable": true, "CertificationRank": 1, "CompanyTrashedAt": null, "BossTypeScores": [{"Directive": 9, "Delegation": 9, "Listening": 5, "Dialogue": 5, "Negotiation": 5}, {"Directive": 1, "Delegation": 5, "Listening": 5, "Dialogue": 5, "Negotiation": 1}, {"Directive": 5, "Delegation": 5, "Listening": 5, "Dialogue": 5, "Negotiation": 5}, {"Directive": 9, "Delegation": 9, "Listening": 5, "Dialogue": 5, "Negotiation": 1}], "ApplyLimit": {"PositionID": 347325, "Enable": true, "Capacity": 10}, "PublishStatus": 1, "Modified": "2022-05-01T01:00:07+09:00", "Title": "GODIVA販売スタッフ【社員割引あり/正社員登用制度あり/家賃補助あり/転勤手当も有り！】", "Post": {"ID": 1, "Name": "役職なし", "Note": ""}, "EmploymentType": {"ID": 2, "Name": "契約社員", "Note": ""}, "Jobs": [{"SmallID": 111112, "Name": "フロアスタッフ（フード／アミューズメント）", "Main": false, "SkillGroups": []}, {"SmallID": 111012, "Name": "販売スタッフ（小売）", "Main": true, "SkillGroups": []}], "MainJobText": "■関東のゴディバ　アウトレット店にてチョコレートの販売、ドリンクの製造をお任せ致します。\n\n＜具体的な仕事内容＞\n・接客対応\n・ドリンクの製造\n・衛生管理\n・レジ会計\n・チョコレートの販売\n・品出し\n・店内清掃", "IncomeFromType": 2, "ModelAnnualIncome": {"Income20s": 250, "Income30s": 300, "Income40s": 450, "Note": ""}, "StockOption": {"ID": 2, "Name": "ストックオプション制度なし", "Note": ""}, "BonusCount": {"ID": 1, "Name": "なし", "Note": ""}, "PromotionCount": {"ID": 2, "Name": "年１回", "Note": ""}, "WorkAddress": {"Values": [{"ID": 1132055, "Name": "東京都青梅市", "Note": ""}, {"ID": 1082074, "Name": "茨城県結城市", "Note": ""}, {"ID": 1082163, "Name": "茨城県笠間市", "Note": ""}, {"ID": 1082171, "Name": "茨城県取手市", "Note": ""}, {"ID": 1082244, "Name": "茨城県守谷市", "Note": ""}, {"ID": 1082252, "Name": "茨城県常陸大宮市", "Note": ""}, {"ID": 1082261, "Name": "茨城県那珂市", "Note": ""}, {"ID": 1082287, "Name": "茨城県坂東市", "Note": ""}, {"ID": 1082341, "Name": "茨城県鉾田市", "Note": ""}, {"ID": 1083097, "Name": "茨城県大洗町", "Note": ""}, {"ID": 1083411, "Name": "茨城県東海村", "Note": ""}, {"ID": 1084425, "Name": "茨城県美浦村", "Note": ""}, {"ID": 1085219, "Name": "茨城県八千代町", "Note": ""}, {"ID": 1092011, "Name": "栃木県宇都宮市", "Note": ""}, {"ID": 1092061, "Name": "栃木県日光市", "Note": ""}, {"ID": 1092118, "Name": "栃木県矢板市", "Note": ""}, {"ID": 1092134, "Name": "栃木県那須塩原市", "Note": ""}, {"ID": 1092142, "Name": "栃木県さくら市", "Note": ""}, {"ID": 1092151, "Name": "栃木県那須烏山市", "Note": ""}, {"ID": 1092169, "Name": "栃木県下野市", "Note": ""}, {"ID": 1093424, "Name": "栃木県益子町", "Note": ""}, {"ID": 1093459, "Name": "栃木県芳賀町", "Note": ""}, {"ID": 1093645, "Name": "栃木県野木町", "Note": ""}, {"ID": 1094072, "Name": "栃木県那須町", "Note": ""}, {"ID": 1094111, "Name": "栃木県那珂川町", "Note": ""}, {"ID": 1111007, "Name": "埼玉県さいたま市", "Note": ""}, {"ID": 1112071, "Name": "埼玉県秩父市", "Note": ""}, {"ID": 1112119, "Name": "埼玉県本庄市", "Note": ""}, {"ID": 1112127, "Name": "埼玉県東松山市", "Note": ""}, {"ID": 1112186, "Name": "埼玉県深谷市", "Note": ""}, {"ID": 1112216, "Name": "埼玉県草加市", "Note": ""}, {"ID": 1112232, "Name": "埼玉県蕨市", "Note": ""}, {"ID": 1112291, "Name": "埼玉県和光市", "Note": ""}, {"ID": 1112348, "Name": "埼玉県八潮市", "Note": ""}, {"ID": 1112356, "Name": "埼玉県富士見市", "Note": ""}, {"ID": 1112372, "Name": "埼玉県三郷市", "Note": ""}, {"ID": 1112381, "Name": "埼玉県蓮田市", "Note": ""}, {"ID": 1112402, "Name": "埼玉県幸手市", "Note": ""}, {"ID": 1112411, "Name": "埼玉県鶴ヶ島市", "Note": ""}, {"ID": 1112429, "Name": "埼玉県日高市", "Note": ""}, {"ID": 1112437, "Name": "埼玉県吉川市", "Note": ""}, {"ID": 1112453, "Name": "埼玉県ふじみ野市", "Note": ""}, {"ID": 1113247, "Name": "埼玉県三芳町", "Note": ""}, {"ID": 1113417, "Name": "埼玉県滑川町", "Note": ""}, {"ID": 1113484, "Name": "埼玉県鳩山町", "Note": ""}, {"ID": 1113492, "Name": "埼玉県ときがわ町", "Note": ""}, {"ID": 1113611, "Name": "埼玉県横瀬町", "Note": ""}, {"ID": 1113620, "Name": "埼玉県皆野町", "Note": ""}, {"ID": 1113638, "Name": "埼玉県長瀞町", "Note": ""}, {"ID": 1113697, "Name": "埼玉県東秩父村", "Note": ""}, {"ID": 1113816, "Name": "埼玉県美里町", "Note": ""}, {"ID": 1114421, "Name": "埼玉県宮代町", "Note": ""}, {"ID": 1114651, "Name": "埼玉県松伏町", "Note": ""}, {"ID": 1122271, "Name": "千葉県浦安市", "Note": ""}, {"ID": 1143847, "Name": "神奈川県湯河原町", "Note": ""}], "Note": ""}, "RemoteWork": {"ID": 1, "Name": "リモート勤務NG", "Note": ""}, "RemoteWorkCondition": "", "RemoteWorkOfficeFrequency": null, "Holiday": {"ID": 3, "Name": "週休2日制", "Note": "休日：シフト制\n月休：8日以上\n有給休暇\n育児休暇\n慶弔休暇"}, "WorkTime": "9:00～21:00\n\n週3日～\n1日6時間以上", "WorkTimeSystem": {"ID": 2, "Name": "シフト制", "Note": ""}, "WorkTimeNightsShift": {"ID": 2, "Name": "夜勤なし", "Note": ""}, "OvertimeAvg": {"ID": 3, "Name": "月平均残業20時間以内", "Note": ""}, "OfficialTripFrequency": {"ID": 1, "Name": "出張なし", "Note": ""}, "WorkEnvironment": [{"ID": 6, "Name": "マイカー通勤可", "Note": "駐車場完備"}], "TransferenceExists": {"ID": 2, "Name": "国内転勤あり（希望した場合のみ）", "Note": ""}, "TransferenceFrequency": {"ID": 4, "Name": "めったにない", "Note": ""}, "TransferenceAbroadExists": {"ID": 2, "Name": "海外転勤なし（予定もなし）", "Note": ""}, "TransferenceAbroadEnglishIsUnused": null, "HREvaluationType": {"Type1": 3, "Type2": 2, "Type3": 3, "Type4": 4, "Note": ""}, "HREvaluationCompetency": {"Axes": [{"Axis": "Sociability", "Value": 2}, {"Axis": "Teamwork", "Value": 2}, {"Axis": "PressureTolerance", "Value": 2}], "Note": ""}, "PR": "「GODIVA」は、ベルギー・ブリュッセルから始まったチョコレートブランドです。\n皆様もご存知のチョコレートの他には「クッキー」「アイスクリーム」「ショコリキサー」と商品を取り揃えており、幅広い年代の方々より親しまれております。\n\nゴディバ独特の芸術性の高いチョコレートは、ゴールドバロタンやシーズンごとに変わる美しいパッケージに詰め込まれ、世界中のグルマンたちに愛されています。\nそんなGODIVAの販売スタッフとして輝きを醸して活躍してください！", "SmokeFree": {"ID": 1, "Name": "あり", "Note": ""}, "SmokeFreeEnvironment": {"ID": 2, "Name": "喫煙室設置", "Note": ""}, "AccomplishmentImportance": {"ID": 4, "Note": "", "Options": [{"ID": 1, "Name": "業績目標未達成者に対し、周囲の評価は厳しく、いづらくなる雰囲気"}, {"ID": 2, "Name": "業績目標未達成者に対し、周囲の評価は厳しく、次のチャレンジを賞賛する"}, {"ID": 3, "Name": "業績目標未達成者に対し、周囲は励ますが、いづらくなる雰囲気"}, {"ID": 4, "Name": "業績目標未達成者に対し、周囲は励まし、次のトライを賞賛する文化"}, {"ID": 5, "Name": "業績目標はあくまで目標であり、重視されてない（もしくはない）"}]}, "AccomplishmentRate": null, "SalesStyleDive": null, "SalesStyleTelAppointment": null, "SalesStyleHost": null, "BaseMonthlySalary": null, "OvertimeSalary": null, "CareerPathOutOfSiteExists": {"ID": 1, "Name": "現場以外のキャリアパスあり", "Note": ""}, "CareerPathWorkHeadOfficeExists": null, "OrgTrendEngineerManagerExists": null, "OrgTrendSectionMemberQty": null, "OrgTrendAccountingLicenceExists": null, "OrgTrendLegalLicenceExists": null, "OrgTrendRelatedWithEngineer": null, "ITEngineerWorkEnvironment": null, "DevelopmentTerm": null, "DevelopmentProcess": null, "EmergencySupport": null, "JoinedReserve": 0, "EmploymentToRegularEmployee": {"ID": 1, "Name": "正社員雇用前提（半年以内）", "Note": ""}, "Probation": {"ID": 7, "Name": "6ヶ月", "Note": "試用期間中は時給からマイナス50円を引いた金額を支給"}, "ContractPeriod": {"ID": 6, "Name": "6ヶ月", "Note": ""}, "ContractExtension": null, "ContractRenewal": {"ID": 1, "Name": "あり"}, "ContractRenewalText": "契約更新の上限回数は３回です。", "TryID": {"ID": 1, "Name": "不可"}, "TryReward": null, "TryDetail": "", "TryDays": null, "TryHours": null, "TryPlace": "", "JobChange": {"Income": {"From": 200, "To": 300, "Note": ""}}, "RegularOutsourcing": null, "SpotOutsourcing": null, "SpotJobDescription": "", "CommissionOutsourcing": null, "OutsourcingAppeal": null, "Interview": {"Shared": {"EstimatedTerm": "2〜3週間程度", "InterviewTimes": 0, "SelectionAptitudeTestExists": false, "SelectionPaperTestExists": false, "SelectionPracticalTestExists": false, "SelectionOtherTestExists": false, "SelectionRemarks": "", "CasualDressFlg": false, "Interviewers": [{"ID": 3, "Name": "現場責任者"}], "OtherInterviewer": ""}, "Meeting": {"EnableFlg": true, "PossibleWeekdayHourFrom": 13, "PossibleWeekdayHourTo": 18, "PossibleWeekendFlg": true, "PossibleTimeComplement": "", "Minutes": 60, "PlaceOptions": ["各店舗になります"], "TransportationPaymentFlg": false, "TransportationPaymentRemarks": ""}, "Online": {"EnableFlg": true, "PossibleWeekdayHourFrom": 13, "PossibleWeekdayHourTo": 18, "PossibleWeekendFlg": true, "PossibleTimeComplement": "", "Minutes": 60}, "Phone": {"EnableFlg": true, "PossibleWeekdayHourFrom": 13, "PossibleWeekdayHourTo": 18, "PossibleWeekendFlg": true, "PossibleTimeComplement": "", "Minutes": 60}, "WorkExperience": {"Pattern": {"ID": 2, "Name": "希望者のみ実施する"}, "Timing": {"ID": 1, "Name": "最終面接後に実施"}, "TimingRemarks": "", "OtherTimingText": "", "WorkTypes": [{"ID": 1, "Name": "職場見学"}], "WorkContent": "テキストが入ります。テキストが入ります。テキストが入ります。テキストが入ります。テキストが入ります。テキストが入ります。テキストが入ります。テキストが入ります。テキストが入ります。テキストが入ります。テキストが入ります。テキストが入ります。テキストが入ります。", "Timeframe": {"ID": 2, "Name": "平日の夜"}, "TimeframeRemarks": "", "NeedTime": {"ID": 2, "Name": "単日（１日）/３時間以上"}, "NeedTimeRemarks": "", "Reward": {"ID": 2, "Name": "報酬あり"}, "RewardValue": 1110, "RewardRemarks": ""}}, "Images": [{"URL": "https://picsum.photos/710/474", "DisplayType": 1}, {"URL": "https://picsum.photos/710/474", "DisplayType": 2}, {"URL": "https://picsum.photos/710/474", "DisplayType": 2}]}, "Company": {"Withdrawal": false, "Name": "株式会社ヒューマン・ベスト"}, "ActionStatus": {"StarredAt": null, "ReadAt": "2022-11-29T14:36:36+09:00", "TrashedAt": null, "PartnerAppliedAt": null}, "Fitting": {"IsMenkaku": false, "TargetMatchRank": 1, "CompetencyMatchRank": 3}, "Target": {"Job": null, "Management": null, "Licence": {"DriverLicence": true, "Licences": []}, "English": null, "Lang": null, "Graduate": null, "Office": null, "Other": null, "Competency": null, "Bias": null}, "Talk": null, "ScoutMessages": {"Scout": {"Content": "あなたの専門的な知識や技術スキルは、私たちのチームに大きな貢献をすることができると考えています。\n一緒に新しい技術を追求し、顧客に最高のサービスを提供していくために、ぜひ私たちのチームに参加してください。", "SentAt": "2023-05-30T10:42:56+09:00"}, "FirstRemind": {"Content": "当社の求人にご興味をお持ちいただき、誠にありがとうございます。\n在宅勤務も可能で、年間休日も125日以上あります。\nオンライン面接をご希望でしたら日時をお知らせ下さい。\n是非、ご応募お待ちしています。", "SentAt": "2023-05-30T10:42:56+09:00"}, "SecondRemind": {"Content": "先日、スカウトメッセージを送信させていただきましたが、確認いただけましたでしょうか？", "SentAt": "2023-05-30T10:42:56+09:00"}}, "IsUnreadScoutMessage": false, "IsUnreadRemindMessage": true}